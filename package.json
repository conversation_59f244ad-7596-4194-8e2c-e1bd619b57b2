{"name": "claude-chat", "displayName": "<PERSON>", "description": "AI-powered chat assistant with <PERSON>", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:claude-code.openChat"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "claude-code.openChat", "title": "Open <PERSON>", "category": "<PERSON>"}], "keybindings": [{"command": "claude-code.openChat", "key": "ctrl+shift+c", "mac": "cmd+shift+c"}, {"command": "workbench.action.webview.reloadWebviewAction", "key": "ctrl+r", "mac": "cmd+r", "when": "activeWebviewPanelId == 'claudeCode'"}], "views": {"claude-code-sidebar": [{"type": "webview", "id": "claude-code.chat", "name": "聊天", "when": "true"}]}, "viewsContainers": {"activitybar": [{"id": "claude-code-sidebar", "title": "<PERSON>", "icon": "$(robot)"}]}, "configuration": {"title": "<PERSON>", "properties": {"claudeChat.wsl.enabled": {"type": "boolean", "default": false, "description": "启用WSL集成"}, "claudeChat.wsl.distro": {"type": "string", "default": "Ubuntu", "description": "WSL发行版名称"}, "claudeChat.wsl.nodePath": {"type": "string", "default": "/usr/bin/node", "description": "WSL中Node.js路径"}, "claudeChat.wsl.claudePath": {"type": "string", "default": "/usr/local/bin/claude", "description": "WSL中Claude CLI路径"}, "claudeChat.model": {"type": "string", "default": "default", "description": "默认Claude模型"}}}}, "scripts": {"git:push": "git add -A && git-pro commit && git pull && git push", "vscode:prepublish": "yarn compile", "compile": "yarn build:webview && tsc -p ./", "watch": "concurrently --kill-others-on-fail --prefix-colors \"bgBlue.bold,bgMagenta.bold\" --names \"VITE,TSC\" \"yarn watch:webview\" \"tsc -watch -p ./\"", "build:webview": "vite build", "watch:webview": "vite build --watch", "dev:webview": "vite", "dev": "powershell -ExecutionPolicy Bypass -File dev.ps1", "serve": "vite", "preview": "vite preview", "test:watch": "echo 测试文件变更检测... && yarn watch", "lint": "eslint src --ext ts,vue", "type-check": "vue-tsc --noEmit"}, "devDependencies": {"@cjh0/git-pro": "^7.0.1", "@types/node": "^20.0.0", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.4.11", "vue-tsc": "^1.8.19"}, "dependencies": {"@types/markdown-it": "^14.1.2", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "vue": "^3.3.4"}}