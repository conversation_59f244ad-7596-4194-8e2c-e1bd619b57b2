<template>
  <div class="chat-container">
    <!-- 头部工具栏 -->
    <div class="header">
      <!-- 左侧按钮组 -->
      <div class="flex items-center space-x-2">
        <button
          @click="startNewSession"
          class="header-btn"
          title="开始新对话"
        >
          <i class="ri-add-line"></i>
        </button>
        <button
          @click="clearChat"
          class="header-btn"
          title="清空聊天记录"
        >
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>

      <!-- 右侧按钮组 -->
      <div class="flex items-center space-x-2">
        <!-- 设置按钮 -->
        <button
          @click="handleSettings"
          class="header-btn"
          title="设置"
        >
          <i class="ri-settings-3-line"></i>
        </button>

        <!-- 调试面板控制按钮 -->
        <button
          @click="toggleDebugPanel"
          :class="['debug-toggle-btn', { active: isDebugPanelVisible }]"
          title="切换调试面板"
        >
          <i class="ri-bug-line"></i>
          调试
        </button>
      </div>
    </div>

    <!-- 消息列表区域 -->
    <div class="messages-container">
      <div ref="messagesRef" class="message-list">
        <!-- 欢迎消息 -->
        <div v-if="chatState.messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <div class="welcome-icon">🤖</div>
            <h3 class="welcome-title">欢迎使用 Claude Chat</h3>
            <p class="welcome-description">我是您的AI编程助手，可以帮助您：</p>
            <ul class="welcome-features">
              <li>💡 代码审查和优化建议</li>
              <li>🐛 调试和问题解决</li>
              <li>📚 技术问题解答</li>
              <li>🚀 最佳实践指导</li>
            </ul>
          </div>
        </div>

        <MessageItem
          v-for="message in chatState.messages"
          :key="message.id"
          :message="message"
        />

        <!-- 滚动锚点 -->
        <div ref="scrollAnchorRef" />
      </div>
    </div>

    <!-- 状态指示器 -->
    <StatusIndicator
      :connection-status="chatState.connectionStatus"
      :total-cost="chatState.totalCost"
      :total-tokens-input="chatState.totalTokensInput"
      :total-tokens-output="chatState.totalTokensOutput"
      :request-count="chatState.requestCount"
      :is-processing="chatState.isProcessing"
      :request-start-time="chatState.requestStartTime"
      @stop="handleStopRequest"
    />

    <!-- 输入区域 -->
    <InputBox
      v-model="currentInput"
      :is-loading="chatState.isLoading"
      :is-processing="chatState.isProcessing"
      @send="handleSendMessage"
      @stop="handleStopRequest"
    />


    <!-- 调试面板 -->
    <DebugPanel 
      :chatState="chatState" 
      :isVisible="isDebugPanelVisible"
      @toggle="toggleDebugPanel"
    />
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref } from "vue";
import { useChat } from "../composables/useChat";
import DebugPanel from "./DebugPanel.vue";
import InputBox from "./InputBox.vue";
import MessageItem from "./MessageItem.vue";
import StatusIndicator from "./StatusIndicator.vue";

// 使用聊天功能
const {
  chatState,
  currentInput,
  sendMessage,
  stopRequest,
  startNewSession,
  clearChat,
  initMessageListener,
  cleanup,
  scrollToBottom,
} = useChat();

// 模板引用
const messagesRef = ref<HTMLElement>();
const scrollAnchorRef = ref<HTMLElement>();

// 调试面板控制
const isDebugPanelVisible = ref(false);

/** 切换调试面板显示 */
function toggleDebugPanel(): void {
  isDebugPanelVisible.value = !isDebugPanelVisible.value;
}

/** 处理设置按钮点击 */
function handleSettings(): void {
  // TODO: 实现设置功能
  console.log('⚙️ 设置功能待开发');
}

/**
 * 处理发送消息
 */
const handleSendMessage = async (content: string) => {
  await sendMessage(content);
  // 发送消息后强制滚动到底部
  await nextTick();
  scrollToBottomSmooth(true);
};

/**
 * 处理停止请求
 */
const handleStopRequest = () => {
  stopRequest();
};

/**
 * 检查用户是否在消息底部
 */
const isUserAtBottom = (): boolean => {
  const container = messagesRef.value
  if (!container) return true
  
  const { scrollTop, scrollHeight, clientHeight } = container
  const threshold = 100 // 100px的缓冲区
  return scrollHeight - scrollTop - clientHeight < threshold
}

/**
 * 智能滚动到底部 - 只有用户在底部时才滚动
 */
const scrollToBottomSmooth = (force = false) => {
  nextTick(() => {
    if (scrollAnchorRef.value && (force || isUserAtBottom())) {
      scrollAnchorRef.value.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  });
};

/**
 * 组件挂载时初始化
 */
onMounted(() => {
  initMessageListener();

  // 监听消息变化，智能自动滚动
  const observer = new MutationObserver((mutations) => {
    // 检查变化是否由用户交互引起（如展开/收起工具消息）
    const isUserInteraction = mutations.some(mutation => {
      const target = mutation.target as Element
      return target.closest?.('.tool-header') || 
             target.closest?.('.tool-content') ||
             target.classList?.contains('tool-expanded') ||
             target.classList?.contains('tool-collapsed')
    })
    
    // 如果不是用户交互，才进行智能滚动
    if (!isUserInteraction) {
      scrollToBottomSmooth();
    }
  });

  if (messagesRef.value) {
    observer.observe(messagesRef.value, {
      childList: true,
      subtree: true,
      characterData: true,
    });
  }

  // 清理函数
  onUnmounted(() => {
    observer.disconnect();
    cleanup();
  });
});

/**
 * 组件卸载时清理
 */
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.chat-container {
  @apply h-screen flex flex-col;
  background-color: var(--vscode-editor-background);
}

.header {
  @apply flex items-center justify-between p-2 border-b;
  border-color: var(--vscode-panel-border);
  background-color: var(--vscode-editor-background);
}

.header-btn {
  @apply flex items-center justify-center w-8 h-8 rounded transition-all;
  background-color: transparent;
  color: var(--vscode-foreground);
  border: 1px solid transparent;
}

.header-btn:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
  border-color: var(--vscode-focusBorder);
}

.header-btn:active {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}



.debug-toggle-btn {
  @apply flex items-center gap-[4px] px-[8px] py-[4px] rounded-[4px] text-xs font-medium transition-all border;
  border-color: var(--vscode-button-border);
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.debug-toggle-btn:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.debug-toggle-btn.active {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border-color: var(--vscode-focusBorder);
}

.messages-container {
  @apply flex-1 overflow-hidden;
}

.message-list {
  @apply h-full overflow-y-auto p-3 space-y-3;
}

/* 欢迎消息样式 */
.welcome-message {
  @apply flex items-center justify-center h-full;
}

.welcome-content {
  @apply text-center max-w-md mx-auto;
}

.welcome-icon {
  @apply text-6xl mb-4;
}

.welcome-title {
  @apply text-xl font-bold mb-2;
  color: var(--vscode-foreground);
}

.welcome-description {
  @apply text-sm mb-4 opacity-75;
  color: var(--vscode-foreground);
}

.welcome-features {
  @apply text-left text-sm space-y-2 mb-6;
  color: var(--vscode-foreground);
}

.welcome-features li {
  @apply flex items-center;
}

.welcome-prompt {
  @apply text-sm opacity-60;
  color: var(--vscode-foreground);
}




</style>
