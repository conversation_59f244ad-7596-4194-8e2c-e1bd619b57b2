﻿# Claude Chat Extension - 万能开发启动脚本
# 一键搞定：yarn watch + 编辑器调试启动
# 智能检测 Cursor/VSCode，自动F5启动调试

param(
    [switch]$Manual = $false  # 手动模式，不自动按F5
)

# 颜色输出
function Write-Status { param($msg, $color="White") Write-Host $msg -ForegroundColor $color }
function Write-Info { param($msg) Write-Host "[INFO] $msg" -ForegroundColor Blue }
function Write-Success { param($msg) Write-Host "[SUCCESS] $msg" -ForegroundColor Green }
function Write-Warning { param($msg) Write-Host "[WARNING] $msg" -ForegroundColor Yellow }
function Write-Error { param($msg) Write-Host "[ERROR] $msg" -ForegroundColor Red }

# 全局变量
$WatchJob = $null
$EditorProcess = $null

# 清理函数
function Cleanup {
    Write-Warning "正在清理所有进程..."
    
    # 停止PowerShell后台任务
    if ($WatchJob) {
        Write-Info "停止watch后台任务..."
        Stop-Job $WatchJob -Force -ErrorAction SilentlyContinue
        Remove-Job $WatchJob -Force -ErrorAction SilentlyContinue
    }
    
    # 清理所有相关Node进程
    Write-Info "清理Node.js进程..."
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    
    if ($nodeProcesses) {
        Write-Info "找到 $($nodeProcesses.Count) 个Node进程，正在清理..."
        $nodeProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
        Start-Sleep 1
        
        # 二次检查
        $remainingNodes = Get-Process -Name "node" -ErrorAction SilentlyContinue
        if ($remainingNodes) {
            Write-Warning "仍有 $($remainingNodes.Count) 个Node进程，强制清理..."
            $remainingNodes | Stop-Process -Force -ErrorAction SilentlyContinue
        }
    }
    
    # 清理其他可能相关的进程
    @("tsc", "vite", "concurrently") | ForEach-Object {
        $procs = Get-Process -Name $_ -ErrorAction SilentlyContinue
        if ($procs) {
            Write-Info "清理 $_ 进程..."
            $procs | Stop-Process -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Success "所有进程清理完成"
    exit 0
}

# Ctrl+C处理
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action { Cleanup }

# 检测编辑器类型
function Get-ActiveEditor {
    Write-Info "检测活跃的编辑器..."
    
    # 优先检测Cursor
    $cursorProcs = Get-Process -Name "Cursor" -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowHandle -ne 0 }
    if ($cursorProcs) {
        $editor = $cursorProcs | Select-Object -First 1
        Write-Success "检测到 Cursor 编辑器 (PID: $($editor.Id))"
        return @{ Name = "Cursor"; Process = $editor; Command = "cursor" }
    }
    
    # 检测VSCode
    $codeProcs = Get-Process -Name "Code" -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowHandle -ne 0 }
    if ($codeProcs) {
        $editor = $codeProcs | Select-Object -First 1
        Write-Success "检测到 VSCode 编辑器 (PID: $($editor.Id))"
        return @{ Name = "VSCode"; Process = $editor; Command = "code" }
    }
    
    return $null
}

# 启动编辑器
function Start-Editor {
    Write-Info "启动编辑器到当前项目..."
    
    # 先尝试Cursor
    if (Get-Command cursor -ErrorAction SilentlyContinue) {
        Write-Info "使用 Cursor 打开项目..."
        Start-Process "cursor" -ArgumentList "." -WindowStyle Hidden
        Start-Sleep 3
        return Get-ActiveEditor
    }
    
    # 再尝试VSCode
    if (Get-Command code -ErrorAction SilentlyContinue) {
        Write-Info "使用 VSCode 打开项目..."
        Start-Process "code" -ArgumentList "." -WindowStyle Hidden
        Start-Sleep 3
        return Get-ActiveEditor
    }
    
    Write-Error "未找到 Cursor 或 VSCode 命令"
    return $null
}

# 发送F5按键
function Send-F5ToEditor {
    param($EditorInfo)
    
    if ($Manual) {
        Write-Warning "手动模式：请按 F5 启动调试"
        return $true
    }
    
    Write-Info "自动发送 F5 按键启动调试..."
    
    try {
        Add-Type -AssemblyName System.Windows.Forms
        Add-Type -AssemblyName Microsoft.VisualBasic
        
        # 激活编辑器窗口
        [Microsoft.VisualBasic.Interaction]::AppActivate($EditorInfo.Process.Id)
        Start-Sleep 2
        
        # 发送F5按键
        [System.Windows.Forms.SendKeys]::SendWait("{F5}")
        Start-Sleep 1
        [System.Windows.Forms.SendKeys]::Send("{F5}")
        
        Write-Success "F5 按键已发送到 $($EditorInfo.Name)"
        return $true
    }
    catch {
        Write-Warning "自动F5失败: $($_.Exception.Message)"
        Write-Warning "请手动按 F5 启动调试"
        return $false
    }
}

# 主函数
function Start-Development {
    Write-Status "" "White"
    Write-Status "🚀 Claude Chat Extension - 万能开发启动" "Cyan"
    Write-Status "=============================================" "Cyan"
    Write-Status "" "White"
    
    # 检查依赖
    Write-Info "检查开发环境..."
    
    if (!(Get-Command yarn -ErrorAction SilentlyContinue)) {
        Write-Error "yarn 未安装"
        return
    }
    
    if (!(Test-Path "package.json")) {
        Write-Error "未找到 package.json，请在项目根目录执行"
        return
    }
    
    Write-Success "环境检查通过"
    Write-Status "" "White"
    
    # 第一步：完整编译项目
    Write-Info "第一步：完整编译项目 (yarn compile)..."
    
    try {
        $compileResult = Start-Process "yarn" -ArgumentList "compile" -Wait -PassThru -NoNewWindow
        
        if ($compileResult.ExitCode -eq 0) {
            Write-Success "项目编译完成"
        } else {
            Write-Error "项目编译失败 (退出码: $($compileResult.ExitCode))"
            Write-Warning "建议检查编译错误后再次运行"
            return
        }
    }
    catch {
        Write-Error "编译过程出错: $($_.Exception.Message)"
        return
    }
    
    Write-Status "" "White"
    
    # 第二步：启动 yarn watch
    Write-Info "第二步：启动 yarn watch (后台监控)..."
    
    $global:WatchJob = Start-Job -ScriptBlock {
        Set-Location $args[0]
        yarn watch
    } -ArgumentList (Get-Location).Path
    
    Start-Sleep 3
    
    if ($WatchJob.State -eq "Running") {
        Write-Success "yarn watch 启动成功"
    } else {
        Write-Error "yarn watch 启动失败"
        return
    }
    
    Write-Status "" "White"
    
    # 检测或启动编辑器
    $editorInfo = Get-ActiveEditor
    
    if (-not $editorInfo) {
        Write-Info "未检测到活跃编辑器，尝试启动..."
        $editorInfo = Start-Editor
    }
    
    if (-not $editorInfo) {
        Write-Error "无法启动编辑器"
        Write-Info "请手动打开 Cursor 或 VSCode，然后按 F5 启动调试"
    } else {
        Write-Status "" "White"
        # 第三步：发送F5启动调试
        Write-Info "第三步：启动调试模式 (F5)..."
        Send-F5ToEditor $editorInfo
    }
    
    Write-Status "" "White"
    Write-Status "✨ 开发环境就绪！" "Green"
    Write-Status "📦 项目已完整编译" "Cyan"
    Write-Status "🔄 yarn watch: 实时监控代码变更" "Cyan"
    Write-Status "🐛 $($editorInfo.Name): 调试模式应已启动" "Cyan"
    Write-Status "" "White"
    Write-Status "按 Ctrl+C 安全退出所有进程" "Yellow"
    Write-Status "" "White"
    
    # 监控循环
    try {
        while ($true) {
            $watchStatus = if ($WatchJob.State -eq "Running") { "✅ 运行中" } else { "❌ 已停止" }
            Write-Host "`r[$(Get-Date -Format 'HH:mm:ss')] yarn watch: $watchStatus | $($editorInfo.Name) 调试中" -NoNewline -ForegroundColor Green
            
            if ($WatchJob.State -ne "Running") {
                Write-Host ""
                Write-Error "yarn watch 进程意外停止"
                break
            }
            
            Start-Sleep 5
        }
    }
    catch {
        Write-Host ""
        Write-Warning "用户中断开发环境"
    }
    finally {
        Cleanup
    }
}

# 显示帮助
if ($args -contains "--help" -or $args -contains "-h") {
    Write-Host "Claude Chat Extension - 万能开发启动脚本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\dev.ps1          # 全自动模式（推荐）"
    Write-Host "  .\dev.ps1 -Manual  # 手动模式，不自动按F5"
    Write-Host ""
    Write-Host "执行流程:"
    Write-Host "  1️⃣ yarn compile - 完整编译项目"
    Write-Host "  2️⃣ yarn watch - 启动实时监控"
    Write-Host "  3️⃣ F5按键 - 自动启动调试"
    Write-Host ""
    Write-Host "功能:"
    Write-Host "  ✅ 三步式启动流程，确保编译完整"
    Write-Host "  ✅ 智能检测 Cursor/VSCode"
    Write-Host "  ✅ 自动发送 F5 启动调试"
    Write-Host "  ✅ 实时监控进程状态"
    Write-Host "  ✅ Ctrl+C 安全退出"
    exit 0
}

# 运行主函数
Start-Development