<template>
  <div class="status-indicator-container">
    <div 
      :class="[
        'status-indicator',
        `status-${connectionStatus}`
      ]"
    >
      <!-- 状态指示灯 -->
      <div class="status-dot"></div>
      
      <!-- 状态文本 -->
      <div class="status-text">
        <span v-if="isProcessing && requestDuration">
          Claude正在工作中 • {{ formatDuration(requestDuration) }}
        </span>
        <span v-else-if="isProcessing">
          连接中...
        </span>
        <span v-else-if="connectionStatus === 'error'">
          连接错误
        </span>
        <span v-else>
          就绪
        </span>
      </div>

      <!-- 统计信息 -->
      <div class="stats-info">
        <span class="cost-info">
          ${{ totalCost.toFixed(6) }}
        </span>
        <span class="tokens-info">
          {{ (totalTokensInput + totalTokensOutput).toLocaleString() }} tokens
        </span>
        <span class="requests-info">
          {{ requestCount }} 请求
        </span>
      </div>

      <!-- 停止按钮已移至输入框 -->
      <!-- <button
        v-if="isProcessing"
        class="stop-button"
        @click="handleStop"
        title="停止请求"
      >
        <i class="ri-stop-fill"></i>
        停止
      </button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

// Props
interface Props {
  connectionStatus: 'ready' | 'processing' | 'error'
  totalCost: number
  totalTokensInput: number
  totalTokensOutput: number
  requestCount: number
  isProcessing: boolean
  requestStartTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  connectionStatus: 'ready',
  totalCost: 0,
  totalTokensInput: 0,
  totalTokensOutput: 0,
  requestCount: 0,
  isProcessing: false
})

// Events
const emit = defineEmits<{
  stop: []
}>()

// 请求持续时间
const requestDuration = ref(0)
let durationTimer: NodeJS.Timeout | null = null

// 添加调试日志
import { watchEffect } from 'vue'
watchEffect(() => {
  console.log('📊 StatusIndicator 状态更新:', {
    connectionStatus: props.connectionStatus,
    isProcessing: props.isProcessing,
    totalCost: props.totalCost,
    requestStartTime: props.requestStartTime,
    totalTokensInput: props.totalTokensInput,
    totalTokensOutput: props.totalTokensOutput,
    requestCount: props.requestCount
  })
})

/**
 * 格式化持续时间
 */
const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`
  }
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}



/**
 * 处理停止请求
 */
const handleStop = () => {
  emit('stop')
}

/**
 * 启动计时器
 */
const startTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
  }
  
  requestDuration.value = 0
  durationTimer = setInterval(() => {
    if (props.requestStartTime) {
      requestDuration.value = Math.floor((Date.now() - props.requestStartTime) / 1000)
    }
  }, 1000)
}

/**
 * 停止计时器
 */
const stopTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
  requestDuration.value = 0
}

/**
 * 组件挂载时初始化
 */
onMounted(() => {
  console.log('✅ StatusIndicator 组件已挂载, 初始状态:', {
    connectionStatus: props.connectionStatus,
    isProcessing: props.isProcessing
  })
  
  if (props.isProcessing && props.requestStartTime) {
    startTimer()
  }
})

onUnmounted(() => {
  stopTimer()
})

// 监听props变化
import { watch } from 'vue'
watch(() => props.isProcessing, (newVal) => {
  if (newVal && props.requestStartTime) {
    startTimer()
  } else {
    stopTimer()
  }
})
</script>

<style scoped>
.status-indicator-container {
  position: relative;
  width: 100%;
  border-top: 1px solid var(--vscode-panel-border);
  background: var(--vscode-input-background);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.status-indicator {
  padding: 4px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 500;
  color: var(--vscode-foreground);
  background: linear-gradient(135deg, 
    var(--vscode-editor-background) 0%, 
    var(--vscode-sideBar-background) 100%);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.status-ready .status-dot {
  background-color: #00d26a;
  box-shadow: 0 0 6px rgba(0, 210, 106, 0.5);
}

.status-processing .status-dot {
  background-color: #ff9500;
  box-shadow: 0 0 6px rgba(255, 149, 0, 0.5);
  animation: pulse 1.5s ease-in-out infinite;
}

.status-error .status-dot {
  background-color: #ff453a;
  box-shadow: 0 0 6px rgba(255, 69, 58, 0.5);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.status-text {
  color: var(--vscode-foreground);
  opacity: 0.9;
  font-weight: 500;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
  font-size: 11px;
  opacity: 0.8;
}

.cost-info {
  color: #00d26a;
  font-weight: 600;
  background: rgba(0, 210, 106, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
  border: 1px solid rgba(0, 210, 106, 0.2);
  font-size: 10px;
}

.tokens-info {
  color: #007acc;
  font-weight: 500;
  background: rgba(0, 122, 204, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
  border: 1px solid rgba(0, 122, 204, 0.2);
  font-size: 10px;
}

.requests-info {
  color: #ff9500;
  font-weight: 500;
  background: rgba(255, 149, 0, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
  border: 1px solid rgba(255, 149, 0, 0.2);
  font-size: 10px;
}

.stop-button {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 2px 6px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.stop-button:hover {
  background: var(--vscode-button-hoverBackground);
}

.stop-button:active {
  transform: scale(0.95);
}

.stop-button svg {
  opacity: 0.8;
}
</style>
