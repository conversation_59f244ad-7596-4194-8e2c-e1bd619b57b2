{"version": "2.0.0", "tasks": [{"type": "shell", "command": "yarn", "args": ["watch"], "group": "build", "label": "dev:watch", "detail": "开发模式 - 自动监听并编译所有代码变更", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}, {"type": "shell", "command": "yarn", "args": ["compile"], "group": "build", "label": "build:all", "detail": "完整构建项目（包括webview和TypeScript）", "problemMatcher": []}, {"type": "shell", "command": "yarn", "args": ["build:webview"], "group": "build", "label": "build:webview", "detail": "构建webview前端资源", "problemMatcher": []}]}